<?php
namespace app\http\middleware;

use app\admin\service\WechatService;

/**
 * 微信授权中间件
 * 检查用户是否已授权，未授权则跳转到微信授权页
 */
class WechatAuth
{
    public function handle($request, \Closure $next)
    {
        // 排除授权控制器
        if ($request->controller() == 'Auth') {
            return $next($request);
        }

        // 处理state参数
        $state = $request->param('state');
        if (!empty($state)) {
            // 判断是否为企业微信授权
            if (WechatService::isWorkState($state)) {
                if (!WechatService::validateWorkState($state)) {
                    return redirect('error/index')->with('error_msg', '无效的企业微信授权请求-301');
                }
            } else {
                if (!WechatService::validateState($state)) {
                    return redirect('error/index')->with('error_msg', '无效的授权请求-301');
                }
            }
        }
        $isWorkAuth = session('isWorkAuth');
        if (!$isWorkAuth) {
            $sessionKey = 'wechat_user_' . session('wx_gzh_id');
        } else {
            $sessionKey = 'work_user_' . session('wx_qy_id');
        }

        // 检查是否已登录
        if (!session($sessionKey)) {
            try {
                // 保存当前URL以便授权后跳转
                session('target_url', $request->url());
                if ($isWorkAuth) {
                    // 获取企业微信授权 - 使用自定义方法强制非静默授权
                    $oauthUrl = WechatService::getWorkOAuthUrlByEasyWeChat();
                    header('Location: ' . $oauthUrl);
                    exit;
                } else {                    
                    // 获取微信授权
                    $app = WechatService::getOfficialAccount();
                    $app->oauth->redirect()->send();
                }
            } catch (\Exception $e) {
                return redirect('error/index')->with('error_msg', '授权失败，请重试');
            }
        }

        return $next($request);
    }
}
