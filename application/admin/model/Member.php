<?php

namespace app\admin\model;

use think\Model;
use think\facade\Request;
use think\Db;

/**
 * 会员模型类
 *
 * 主要功能：
 * 1. 管理会员的微信用户信息
 * 2. 提供会员相关的业务操作方法
 */
class Member extends Model
{
    /**
     * 用户类型常量
     */
    const TYPE_DEALER = 1;    // 经销商
    const TYPE_GROUP_MGR = 2; // 群管
    const TYPE_MEMBER = 3;    // 会员
    const TYPE_PUBLIC = 4;    // 公海会员

    /**
     * 用户状态常量
     */
    const STATUS_PENDING = 1; // 待审核
    const STATUS_APPROVED = 9; // 已通过

    /**
     * 错误码常量
     */
    const ERROR_OTHER_WXGZH = 414;    // 用户已归属其他公众号
    const ERROR_IS_DEALER = 415;      // 用户已是经销商
    const ERROR_IS_GROUP_MGR = 416;   // 用户已是群管
    const ERROR_OTHER_DEALER = 417;   // 用户已归属其他经销商
    const ERROR_OTHER_GROUP_MGR = 418; // 用户已归属其他群管

    /**
     * 获取或创建微信用户信息
     *
     * @param string $openid 微信用户的openid
     * @return array 包含状态码、消息和数据的数组
     */
    public static function getOrCreateWxUser($openid)
    {
        // 参数验证
        if (empty($openid)) {
            return self::createResult(400, '微信用户标识不能为空');
        }

        // 获取当前公众号、经销商和群管的会话键
        $wxgzhId = session('wx_gzh_id');
        $dealerId = session('wx_dealer_id');
        $groupId = session('wx_group_id');
        $sessionKey = 'wechat_user_'. $wxgzhId;
        $isWorkAuth = session('isWorkAuth');
        $map = [
            'openid' => $openid,
            'del' => 0,
        ];
        if ($isWorkAuth) {
            $map['wx_type'] = 1;
        } else {
            $map['wx_type'] = 2;
        }
        // 查询用户是否已存在
        $userRow = Db::name('org_wxuser')
            ->where($map)
            ->find();

        // 用户已存在的情况处理
        if (!empty($userRow)) {
            return self::handleExistingUser($userRow, $wxgzhId, $dealerId, $groupId);
        }

        // 用户不存在，创建新用户
        return self::createNewWxUser($openid);
    }

    /**
     * 处理已存在的用户
     *
     * @param array $userRow 用户数据
     * @param string $wxgzhId 微信公众号ID
     * @param int $dealerId 经销商ID
     * @param int $groupId 群管ID
     * @return array 处理结果
     */
    private static function handleExistingUser($userRow, $wxgzhId, $dealerId, $groupId)
    {
        // 检查用户是否属于当前公众号
        if ($userRow['wxgzh_id'] != $wxgzhId) {
            return self::createResult(self::ERROR_OTHER_WXGZH, '当前用户已归属其他公众号');
        }

        // 根据用户类型进行不同处理
        switch ($userRow['type']) {
            case self::TYPE_PUBLIC:
                // 公海会员重新注册为会员
                $updateData = [
                    'type' => self::TYPE_MEMBER,
                    'status' => self::STATUS_APPROVED,
                    'agency_id' => $dealerId ?? 0,
                    'tube_id' => $groupId ?? 0,
                    'update_time' => time()
                ];
                Db::name('org_wxuser')
                    ->where('user_id', $userRow['user_id'])
                    ->update($updateData);

                // 更新本地用户数据
                $userRow['type'] = self::TYPE_MEMBER;
                $userRow['status'] = self::STATUS_APPROVED;
                $userRow['agency_id'] = $dealerId ?? 0;
                $userRow['tube_id'] = $groupId ?? 0;
                break;

            case self::TYPE_DEALER:
                return self::createResult(self::ERROR_IS_DEALER, '当前用户已注册经销商');

            case self::TYPE_GROUP_MGR:
                return self::createResult(self::ERROR_IS_GROUP_MGR, '当前用户已注册群管');
        }

        // 检查用户是否属于当前经销商
        if ($userRow['agency_id'] != $dealerId) {
            return self::createResult(self::ERROR_OTHER_DEALER, '当前用户已归属其他经销商');
        }

        // 检查用户是否属于当前群管
        if ($userRow['tube_id'] != $groupId) {
            return self::createResult(self::ERROR_OTHER_GROUP_MGR, '当前用户已归属其他群管');
        }

        return self::createResult(200, 'success', $userRow);
    }

    /**
     * 创建新的微信用户
     *
     * @param string $openid 微信用户的openid
     * @param string $wxgzhId 微信公众号ID
     * @param int $dealerId 经销商ID
     * @param int $groupId 群管ID
     * @param string $sessionKey 会话键
     * @return array 创建结果
     */
    private static function createNewWxUser($openid)
    {
        $wxgzhId = session('wx_gzh_id');
        $dealerId = session('wx_dealer_id');
        $groupId = session('wx_group_id');
        $sessionKey = 'wechat_user_'. $wxgzhId;
        $isWorkAuth = session('isWorkAuth');
        $qySessionKey = 'work_user_' . session('wx_qy_id');
        if ($isWorkAuth) {
            $nickname = session("$qySessionKey.name");
            $avatar = session("$qySessionKey.avatar");
        } else {
            $nickname = session("$sessionKey.nickname");
            $avatar = session("$sessionKey.avatar");
        }   
        // 准备插入数据
        $insertData = [
            'type' => self::TYPE_MEMBER,
            'wx_type' => $isWorkAuth ? 1 : 2,
            'wxgzh_id' => $wxgzhId,
            'org_id' => getWxgzhBindMchId($wxgzhId),
            'agency_id' => $dealerId ?? 0,
            'tube_id' => $groupId ?? 0,
            'openid' => $openid,
            'user_name' => base64_encode($nickname),
            'nickname' => base64_encode($nickname),
            'avatar' => $avatar,
            'qy_user_id' => session("$qySessionKey.id"),
            'qy_wx_id' => session('wx_qy_id'),
            'ip' => Request::ip(),
            'last_visit' => time(),
            'status' => self::STATUS_APPROVED,
            'create_time' => time()
        ];

        // 插入数据并获取新用户ID
        $userId = Db::name('org_wxuser')->insertGetId($insertData);

        // 查询新创建的用户数据
        $userRow = $userId ? Db::name('org_wxuser')->where('user_id', $userId)->find() : null;

        if (!$userRow) {
            return self::createResult(500, '创建用户失败');
        }

        return self::createResult(200, 'success', $userRow);
    }

    /**
     * 创建统一格式的返回结果
     *
     * @param int $code 状态码
     * @param string $msg 消息
     * @param array $data 数据
     * @return array 格式化的结果
     */
    private static function createResult($code, $msg, $data = [])
    {
        return [
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ];
    }

    /**
     * 获取会员详细信息
     *
     * @param int $userId 会员ID
     * @return array 会员详细信息
     */
    public static function getMemberDetail($userId)
    {
        if (empty($userId)) {
            return [];
        }

        return Db::name('org_wxuser')
            ->alias('u')
            ->leftJoin('org_agency a', 'u.agency_id = a.agency_id')
            ->leftJoin('org_agency_tube t', 'u.tube_id = t.tube_id')
            ->where(['u.user_id' => $userId, 'u.del' => 0, 'u.type' => self::TYPE_MEMBER])
            ->field('u.*, a.agency_name, t.tube_name')
            ->find();
    }

    /**
     * 更新会员状态
     *
     * @param int $userId 会员ID
     * @param int $status 状态值
     * @return boolean 更新结果
     */
    public static function updateMemberStatus($userId, $status)
    {
        if (empty($userId)) {
            return false;
        }

        $updateData = [
            'status' => $status,
            'update_time' => time()
        ];

        return Db::name('org_wxuser')
            ->where(['user_id' => $userId, 'type' => self::TYPE_MEMBER])
            ->update($updateData) !== false;
    }

    /**
     * 获取会员列表
     *
     * @param int $dealerId 经销商ID
     * @param int $groupId 群管ID
     * @param array $filter 筛选条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array 会员列表和分页信息
     */
    public static function getMemberList($dealerId = 0, $groupId = 0, $filter = [], $page = 1, $limit = 15)
    {
        $where = [
            'u.type' => self::TYPE_MEMBER,
            'u.del' => 0
        ];

        // 添加经销商筛选
        if (!empty($dealerId)) {
            $where['u.agency_id'] = $dealerId;
        }

        // 添加群管筛选
        if (!empty($groupId)) {
            $where['u.tube_id'] = $groupId;
        }

        // 添加状态筛选
        if (isset($filter['status']) && $filter['status'] > 0) {
            $where['u.status'] = $filter['status'];
        }

        // 添加关键词搜索
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = [
                'exp',
                "u.user_name LIKE '%{$keyword}%' OR u.nickname LIKE '%{$keyword}%' OR u.mobile LIKE '%{$keyword}%' OR u.real_name LIKE '%{$keyword}%'"
            ];
        }

        // 查询总数
        $total = Db::name('org_wxuser')
            ->alias('u')
            ->where($where)
            ->count();

        // 查询列表
        $list = Db::name('org_wxuser')
            ->alias('u')
            ->leftJoin('org_agency a', 'u.agency_id = a.agency_id')
            ->leftJoin('org_agency_tube t', 'u.tube_id = t.tube_id')
            ->where($where)
            ->field('u.*, a.agency_name, t.tube_name')
            ->page($page, $limit)
            ->order('u.create_time DESC')
            ->select();

        // 计算各状态会员数量
        $memberCount = Db::name('org_wxuser')
            ->where([
                'type' => self::TYPE_MEMBER,
                'status' => self::STATUS_APPROVED,
                'del' => 0
            ])
            ->when(!empty($dealerId), function ($query) use ($dealerId) {
                $query->where('agency_id', $dealerId);
            })
            ->when(!empty($groupId), function ($query) use ($groupId) {
                $query->where('tube_id', $groupId);
            })
            ->count();

        $darkCount = Db::name('org_wxuser')
            ->where([
                'type' => self::TYPE_MEMBER,
                'status' => 4, // 禁用状态
                'del' => 0
            ])
            ->when(!empty($dealerId), function ($query) use ($dealerId) {
                $query->where('agency_id', $dealerId);
            })
            ->when(!empty($groupId), function ($query) use ($groupId) {
                $query->where('tube_id', $groupId);
            })
            ->count();

        return [
            'list' => $list,
            'page' => ceil($total / $limit),
            'total' => $total,
            'member' => $memberCount,
            'dark' => $darkCount,
            'has_more' => ($page * $limit) < $total
        ];
    }

    /**
     * 更新会员信息
     *
     * @param int $userId 会员ID
     * @param array $data 更新数据
     * @return boolean 更新结果
     */
    public static function updateMemberInfo($userId, $data)
    {
        if (empty($userId) || empty($data)) {
            return false;
        }

        $updateData = array_merge($data, ['update_time' => time()]);

        return Db::name('org_wxuser')
            ->where(['user_id' => $userId, 'type' => self::TYPE_MEMBER])
            ->update($updateData) !== false;
    }
}
