<?php

namespace app\admin\model;

use think\Model;
use think\facade\Request;
use think\Db;

/**
 * 群管模型类
 *
 * 主要功能：
 * 1. 管理群管的微信用户信息
 * 2. 管理群管的基本信息
 */
class GroupMgr extends Model
{
    /**
     * 用户类型常量
     */
    const TYPE_DEALER = 1;    // 经销商
    const TYPE_GROUP_MGR = 2; // 群管
    const TYPE_MEMBER = 3;    // 会员
    const TYPE_PUBLIC = 4;    // 公海会员

    /**
     * 用户状态常量
     */
    const STATUS_PENDING = 1; // 待审核
    const STATUS_APPROVED = 9; // 已通过

    /**
     * 错误码常量
     */
    const ERROR_OTHER_WXGZH = 415;     // 用户已归属其他公众号
    const ERROR_OTHER_DEALER = 416;    // 用户已归属其他经销商
    const ERROR_ALREADY_DEALER = 418; // 用户已是经销商
    const ERROR_ALREADY_MEMBER = 419; // 用户已是会员

    /**
     * 获取或创建微信用户信息
     *
     * @param string $openid 微信用户的openid
     * @return array 包含状态码、消息和数据的数组
     */
    public static function getOrCreateWxUser($openid)
    {
        // 参数验证
        if (empty($openid)) {
            return self::createResult(400, '微信用户标识不能为空 - mgr');
        }

        // 获取当前公众号和经销商的会话键
        $wxgzhId = session('wx_gzh_id');
        $dealerId = session('wx_dealer_id');
        $sessionKey = 'wechat_user_' . $wxgzhId;
        $isWorkAuth = session('isWorkAuth');
        $map = [
            'openid' => $openid,
            'del' => 0,
        ];
        if ($isWorkAuth) {
            $map['wx_type'] = 1;
        } else {
            $map['wx_type'] = 2;
        }
        // 查询用户是否已存在
        $userRow = Db::name('org_wxuser')
            ->where($map)
            ->find();

        // 用户已存在的情况处理
        if (!empty($userRow)) {
            return self::handleExistingUser($userRow, $wxgzhId, $dealerId);
        }

        // 用户不存在，创建新用户
        return self::createNewWxUser($openid);
    }

    /**
     * 处理已存在的用户
     *
     * @param array $userRow 用户数据
     * @param string $wxgzhId 微信公众号ID
     * @param int $dealerId 经销商ID
     * @return array 处理结果
     */
    private static function handleExistingUser($userRow, $wxgzhId, $dealerId)
    {
        // 检查用户是否属于当前公众号
        if ($userRow['wxgzh_id'] != $wxgzhId) {
            return self::createResult(self::ERROR_OTHER_WXGZH, '当前用户已归属其他公众号');
        }

        // 根据用户类型进行不同处理
        switch ($userRow['type']) {
            case self::TYPE_PUBLIC:
                // 公海会员重新注册为群管
                $updateData = [
                    'type' => self::TYPE_GROUP_MGR,
                    'status' => self::STATUS_PENDING,
                    'agency_id' => $dealerId,
                    'update_time' => time()
                ];
                Db::name('org_wxuser')
                    ->where('user_id', $userRow['user_id'])
                    ->update($updateData);

                // 更新本地用户数据
                $userRow['type'] = self::TYPE_GROUP_MGR;
                $userRow['status'] = self::STATUS_PENDING;
                break;

            case self::TYPE_DEALER:
                return self::createResult(self::ERROR_ALREADY_DEALER, '当前用户已注册经销商，请勿重复注册');

            case self::TYPE_MEMBER:
                return self::createResult(self::ERROR_ALREADY_MEMBER, '当前用户已注册会员，请勿重复注册');
        }

        // 检查用户是否属于当前经销商
        if ($userRow['agency_id'] != $dealerId) {
            return self::createResult(self::ERROR_OTHER_DEALER, '当前用户已归属其他经销商');
        }

        return self::createResult(200, 'success', $userRow);
    }

    /**
     * 创建新的微信用户
     *
     * @param string $openid 微信用户的openid
     * @param string $wxgzhId 微信公众号ID
     * @param int $dealerId 经销商ID
     * @param string $sessionKey 会话键
     * @return array 创建结果
     */
    private static function createNewWxUser($openid)
    {
        $wxgzhId = session('wx_gzh_id');
        $sessionKey = 'wechat_user_' . $wxgzhId;
        $isWorkAuth = session('isWorkAuth');
        $qySessionKey = 'work_user_' . session('wx_qy_id');
        if ($isWorkAuth) {
            $nickname = session("$qySessionKey.name");
            $avatar = session("$qySessionKey.avatar");
        } else {
            $nickname = session("$sessionKey.nickname");
            $avatar = session("$sessionKey.avatar");
        }
        // 准备插入数据
        $insertData = [
            'type' => self::TYPE_GROUP_MGR,
            'wx_type' => $isWorkAuth ? 1 : 2,
            'wxgzh_id' => $wxgzhId,
            'org_id' => getWxgzhBindMchId($wxgzhId),
            'openid' => $openid,
            'nickname' => base64_encode($nickname),
            'avatar' => $avatar,
            'qy_user_id' => session("$qySessionKey.id"),
            'qy_wx_id' => session('wx_qy_id'),
            'ip' => Request::ip(),
            'last_visit' => time(),
            'status' => self::STATUS_PENDING,
            'create_time' => time()
        ];

        // 插入数据并获取新用户ID
        $userId = Db::name('org_wxuser')->insertGetId($insertData);

        // 查询新创建的用户数据
        $userRow = $userId ? Db::name('org_wxuser')->where('user_id', $userId)->find() : null;

        if (!$userRow) {
            return self::createResult(500, '创建用户失败');
        }

        return self::createResult(200, 'success', $userRow);
    }

    /**
     * 获取或创建群管信息
     *
     * @param int $wxuserId 微信用户ID
     * @return array|null 群管信息
     */
    public static function getOrCreateGroupMgr($wxuserId)
    {
        // 参数验证
        if (empty($wxuserId)) {
            return null;
        }

        // 查询群管是否已存在
        $tubeRow = Db::name('org_agency_tube')
            ->where(['wx_user_id' => $wxuserId, 'del' => 0])
            ->find();

        // 群管不存在，创建新群管
        if (!$tubeRow) {
            $tubeRow = self::createNewGroupMgr($wxuserId);
        }

        return $tubeRow;
    }

    /**
     * 创建新的群管
     *
     * @param int $wxuserId 微信用户ID
     * @param string $wxgzhId 微信公众号ID
     * @param int $dealerId 经销商ID
     * @param string $sessionKey 会话键
     * @return array|null 创建的群管信息
     */
    private static function createNewGroupMgr($wxuserId)
    {
        $wxgzhId = session('wx_gzh_id');
        $dealerId = session('wx_dealer_id');
        $sessionKey = 'wechat_user_' . $wxgzhId;
        $isWorkAuth = session('isWorkAuth');
        $qySessionKey = 'work_user_' . session('wx_qy_id');
        if ($isWorkAuth) {
            $nickname = session("$qySessionKey.name");
            $avatar = session("$qySessionKey.avatar");
        } else {
            $nickname = session("$sessionKey.nickname");
            $avatar = session("$sessionKey.avatar");
        }
        // 获取经销商名称
        $agencyName = Db::name('org_agency')
            ->where('agency_id', $dealerId)
            ->value('agency_name') ?? '';

        // 准备插入数据
        $insertData = [
            'wx_user_id' => $wxuserId,
            'wxgzh_id' => $wxgzhId,
            'org_id' => getWxgzhBindMchId($wxgzhId),
            'agency_id' => $dealerId ?? 0,
            'agency_name' => $agencyName,
            'tube_name' => base64_encode($nickname),
            'type' => $isWorkAuth ? 1 : 2,
            'nickname' => base64_encode($nickname),
            'avatar' => $avatar,
            'qy_user_id' => session("$qySessionKey.id"),
            'qy_wx_id' => session('wx_qy_id'),
            'ip' => Request::ip(),
            'status' => self::STATUS_PENDING,
            'create_time' => time()
        ];

        // 插入数据并获取新群管ID
        $tubeId = Db::name('org_agency_tube')->insertGetId($insertData);

        // 查询新创建的群管数据
        return $tubeId ? Db::name('org_agency_tube')->where('tube_id', $tubeId)->find() : null;
    }

    /**
     * 创建统一格式的返回结果
     *
     * @param int $code 状态码
     * @param string $msg 消息
     * @param array $data 数据
     * @return array 格式化的结果
     */
    private static function createResult($code, $msg, $data = [])
    {
        return [
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ];
    }

    /**
     * 获取群管详细信息
     *
     * @param int $tubeId 群管ID
     * @return array 群管详细信息
     */
    public static function getGroupMgrDetail($tubeId)
    {
        if (empty($tubeId)) {
            return [];
        }

        return Db::name('org_agency_tube')
            ->alias('t')
            ->join('org_wxuser w', 't.wx_user_id = w.user_id')
            ->where(['t.tube_id' => $tubeId, 't.del' => 0])
            ->field('t.*, w.openid, w.nickname as wx_nickname, w.avatar as wx_avatar')
            ->find();
    }

    /**
     * 更新群管状态
     *
     * @param int $tubeId 群管ID
     * @param int $status 状态值
     * @return boolean 更新结果
     */
    public static function updateGroupMgrStatus($tubeId, $status)
    {
        if (empty($tubeId)) {
            return false;
        }

        $updateData = [
            'status' => $status,
            'update_time' => time()
        ];

        return Db::name('org_agency_tube')
            ->where('tube_id', $tubeId)
            ->update($updateData) !== false;
    }

    /**
     * 获取经销商下的群管列表
     *
     * @param int $dealerId 经销商ID
     * @param array $filter 筛选条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array 群管列表和分页信息
     */
    public static function getGroupMgrList($dealerId, $filter = [], $page = 1, $limit = 15)
    {
        if (empty($dealerId)) {
            return ['list' => [], 'page' => null, 'total' => 0];
        }

        $where = [
            't.agency_id' => $dealerId,
            't.del' => 0
        ];

        // 添加状态筛选
        if (isset($filter['status']) && $filter['status'] > 0) {
            $where['t.status'] = $filter['status'];
        }

        // 添加关键词搜索
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = [
                'exp',
                "t.tube_name LIKE '%{$keyword}%' OR w.nickname LIKE '%{$keyword}%' OR w.mobile LIKE '%{$keyword}%'"
            ];
        }

        // 查询总数
        $total = Db::name('org_agency_tube')
            ->alias('t')
            ->join('org_wxuser w', 't.wx_user_id = w.user_id')
            ->where($where)
            ->count();

        // 查询列表
        $list = Db::name('org_agency_tube')
            ->alias('t')
            ->join('org_wxuser w', 't.wx_user_id = w.user_id')
            ->where($where)
            ->field('t.*, w.openid, w.nickname as wx_nickname, w.avatar as wx_avatar, w.mobile')
            ->page($page, $limit)
            ->order('t.create_time DESC')
            ->select();

        return [
            'list' => $list,
            'page' => ceil($total / $limit),
            'total' => $total
        ];
    }
}
