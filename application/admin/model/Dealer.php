<?php

namespace app\admin\model;

use think\Model;
use think\facade\Request;
use think\Db;

/**
 * 经销商模型类
 *
 * 主要功能：
 * 1. 管理经销商的微信用户信息
 * 2. 管理经销商的基本信息
 */
class Dealer extends Model
{
    /**
     * 用户类型常量
     */
    const TYPE_DEALER = 1;    // 经销商
    const TYPE_GROUP_MGR = 2; // 群管
    const TYPE_MEMBER = 3;    // 会员
    const TYPE_PUBLIC = 4;    // 公海会员

    /**
     * 用户状态常量
     */
    const STATUS_PENDING = 1; // 待审核
    const STATUS_APPROVED = 9; // 已通过

    /**
     * 错误码常量
     */
    const ERROR_OTHER_WXGZH = 414; // 用户已归属其他公众号
    const ERROR_ALREADY_GROUP_MGR = 415; // 用户已是群管
    const ERROR_ALREADY_MEMBER = 416; // 用户已是会员

    /**
     * 获取或创建微信用户信息
     *
     * @param string $openid 微信用户的openid
     * @return array 包含状态码、消息和数据的数组
     */
    public static function getOrCreateWxUser($openid)
    {
        // 参数验证
        if (empty($openid)) {
            return self::createResult(400, '微信用户标识不能为空');
        }

        // 获取当前公众号的会话键
        $wxgzhId = session('wx_gzh_id');

        $isWorkAuth = session('isWorkAuth');
        $map = [
            'openid' => $openid,
            'del' => 0,
        ];
        if ($isWorkAuth) {
            $map['wx_type'] = 1;
        } else {
            $map['wx_type'] = 2;
        }

        // 查询用户是否已存在
        $userRow = Db::name('org_wxuser')
            ->where($map)
            ->find();

        // 用户已存在的情况处理
        if (!empty($userRow)) {
            return self::handleExistingUser($userRow, $wxgzhId);
        }

        // 用户不存在，创建新用户
        return self::createNewWxUser($openid);
    }

    /**
     * 处理已存在的用户
     *
     * @param array $userRow 用户数据
     * @param string $wxgzhId 微信公众号ID
     * @return array 处理结果
     */
    private static function handleExistingUser($userRow, $wxgzhId)
    {
        // 检查用户是否属于当前公众号
        if ($userRow['wxgzh_id'] != $wxgzhId) {
            return self::createResult(self::ERROR_OTHER_WXGZH, '当前用户已归属其他公众号');
        }

        // 根据用户类型进行不同处理
        switch ($userRow['type']) {
            case self::TYPE_PUBLIC:
                // 公海会员重新注册为经销商
                $updateData = [
                    'type' => self::TYPE_DEALER,
                    'status' => self::STATUS_PENDING,
                    'update_time' => time()
                ];
                Db::name('org_wxuser')
                    ->where('user_id', $userRow['user_id'])
                    ->update($updateData);

                // 更新本地用户数据
                $userRow['type'] = self::TYPE_DEALER;
                $userRow['status'] = self::STATUS_PENDING;
                break;

            case self::TYPE_GROUP_MGR:
                return self::createResult(self::ERROR_ALREADY_GROUP_MGR, '当前用户已注册群管，请勿重复注册');

            case self::TYPE_MEMBER:
                return self::createResult(self::ERROR_ALREADY_MEMBER, '当前用户已注册会员，请勿重复注册');
        }

        return self::createResult(200, 'success', $userRow);
    }

    /**
     * 创建新的微信用户
     *
     * @param string $openid 微信用户的openid
     * @param string $wxgzhId 微信公众号ID
     * @param string $sessionKey 会话键
     * @return array 创建结果
     */
    private static function createNewWxUser($openid)
    {
        $wxgzhId = session('wx_gzh_id');
        $sessionKey = 'wechat_user_'. $wxgzhId;
        $qySessionKey = 'work_user_' . session('wx_qy_id');
        $isWorkAuth = session('isWorkAuth');
        if ($isWorkAuth) {
            $wxType = 1;
            $nickname = session("$qySessionKey.name");
            $avatar = session("$qySessionKey.avatar");
        } else {
            $wxType = 2;
            $nickname = session("$sessionKey.nickname");
            $avatar = session("$sessionKey.avatar");
        }
        // 准备插入数据
        $insertData = [
            'wx_type' => $wxType,
            'type' => self::TYPE_DEALER,
            'wxgzh_id' => $wxgzhId,
            'org_id' => getWxgzhBindMchId($wxgzhId),
            'openid' => $openid,
            'nickname' => base64_encode($nickname),
            'avatar' => $avatar,
            'qy_user_id' => session("$qySessionKey.id"),
            'qy_wx_id' => session('wx_qy_id'),
            'ip' => Request::ip(),
            'last_visit' => time(),
            'status' => self::STATUS_PENDING,
            'create_time' => time()
        ];

        // 插入数据并获取新用户ID
        $userId = Db::name('org_wxuser')->insertGetId($insertData);

        // 查询新创建的用户数据
        $userRow = $userId ? Db::name('org_wxuser')->where('user_id', $userId)->find() : null;

        if (!$userRow) {
            return self::createResult(500, '创建用户失败');
        }

        return self::createResult(200, 'success', $userRow);
    }

    /**
     * 获取或创建经销商信息
     *
     * @param int $wxuserId 微信用户ID
     * @return array|null 经销商信息
     */
    public static function getOrCreateAgency($wxuserId)
    {
        // 参数验证
        if (empty($wxuserId)) {
            return null;
        }

        // 查询经销商是否已存在
        $agencyRow = Db::name('org_agency')
            ->where(['wx_user_id' => $wxuserId, 'del' => 0])
            ->find();

        // 经销商不存在，创建新经销商
        if (!$agencyRow) {
            $agencyRow = self::createNewAgency($wxuserId);
        }

        return $agencyRow;
    }

    /**
     * 创建新的经销商
     *
     * @param int $wxuserId 微信用户ID
     * @return array|null 创建的经销商信息
     */
    private static function createNewAgency($wxuserId)
    {
        $wxgzhId = session('wx_gzh_id');
        $sessionKey = 'wechat_user_'. $wxgzhId;
        $qySessionKey = 'work_user_' . session('wx_qy_id');
        $isWorkAuth = session('isWorkAuth');
        if ($isWorkAuth) {
            $nickname = session("$qySessionKey.name");
            $avatar = session("$qySessionKey.avatar");
        } else {
            $nickname = session("$sessionKey.nickname");
            $avatar = session("$sessionKey.avatar");
        }
        // 准备插入数据
        $insertData = [
            'wx_user_id' => $wxuserId,
            'wxgzh_id' => $wxgzhId,
            'org_id' => getWxgzhBindMchId($wxgzhId),
            'agency_name' => base64_encode($nickname),
            'type' => $isWorkAuth ? 1 : 2, // 1: 企业微信, 2: 微信公众号
            'nickname' => base64_encode($nickname),
            'avatar' => $avatar,
            'qy_user_id' => session("$qySessionKey.id"),
            'qy_wx_id' => session('wx_qy_id'),
            'ip' => Request::ip(),
            'status' => self::STATUS_PENDING,
            'create_time' => time()
        ];

        // 插入数据并获取新经销商ID
        $agencyId = Db::name('org_agency')->insertGetId($insertData);

        // 查询新创建的经销商数据
        return $agencyId ? Db::name('org_agency')->where('agency_id', $agencyId)->find() : null;
    }

    /**
     * 创建统一格式的返回结果
     *
     * @param int $code 状态码
     * @param string $msg 消息
     * @param array $data 数据
     * @return array 格式化的结果
     */
    private static function createResult($code, $msg, $data = [])
    {
        return [
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ];
    }

    /**
     * 获取经销商详细信息
     *
     * @param int $agencyId 经销商ID
     * @return array 经销商详细信息
     */
    public static function getAgencyDetail($agencyId)
    {
        if (empty($agencyId)) {
            return [];
        }

        return Db::name('org_agency')
            ->alias('a')
            ->join('org_wxuser w', 'a.wx_user_id = w.user_id')
            ->where(['a.agency_id' => $agencyId, 'a.del' => 0])
            ->field('a.*, w.openid, w.nickname as wx_nickname, w.avatar as wx_avatar')
            ->find();
    }

    /**
     * 更新经销商状态
     *
     * @param int $agencyId 经销商ID
     * @param int $status 状态值
     * @return boolean 更新结果
     */
    public static function updateAgencyStatus($agencyId, $status)
    {
        if (empty($agencyId)) {
            return false;
        }

        $updateData = [
            'status' => $status,
            'update_time' => time()
        ];

        return Db::name('org_agency')
            ->where('agency_id', $agencyId)
            ->update($updateData) !== false;
    }
}
