<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 流年 <<EMAIL>>
// +----------------------------------------------------------------------

use think\Db;
use think\facade\Cache;
// 应用公共文件
// 
function getPassword($pwd){
    return password_hash(trim($pwd), PASSWORD_DEFAULT);
}
/**
 * 获取列表序列号
 */
function get_sequence($page_row = 1, $idx){
    if(isset($_REQUEST['p']) and !empty($_REQUEST['p'])){
        $page_no = $_REQUEST['p'] - 1;
    }else{
        $page_no = 0;
    }
    $seq = $page_no * $page_row + $idx;
    return $seq;
}

/**
 * 获取用户组分组数据
 * @param  [type] $status [description]
 * @param  [type] $field  [description]
 * @return [type]         [description]
 */
function getRoleGroup($status = null, $field = null){
    $list = array(
        1 => array('id'=>'1', 'name'=>'商户管理中心'),
    );
    if(empty($status) and empty($field)){
        return $list;
    }else{
        if(empty($field)){
            $field = 'name';
        }
        return $list[$status][$field];
    }
}

/**
 * 获取分组后的用户组数据
 * @return [type] [description]
 */
function getRoleGroupRows(){
    //查询分组下的用户组
    $group = getRoleGroup();
    foreach ($group as $key => $value) {
        $map[] = ['status','=',1];
        $map[] = ['group_id','=',$value['id']];
        $map[] = ['org_id','in',[0, session('admin.org_id')]];
        $child = db('org_role')->where($map)->select();
        if(empty($child)){
            unset($group[$key]);
            continue;
        }
        $group[$key]['child'] = $child;
    }
    $group = array_values($group);

    //拼接无分组的用户组
    $b_map['status'] = 1;
    $b_map['group_id'] = 0;
    $no_group_child = db('org_role')->where($b_map)->select();
    if(!empty($no_group_child)){
        $num = count($group);
        $group[$num]['id'] = 0;
        $group[$num]['name'] = '无分组数据';
        $group[$num]['child'] = $no_group_child;
    }

    return $group;
}

/**
 * 返回数组中指定的一列
 * @param $input            需要取出数组列的多维数组（或结果集）
 * @param $columnKey        需要返回值的列，它可以是索引数组的列索引，或者是关联数组的列的键。 也可以是NULL，此时将返回整个数组（配合index_key参数来重置数组键的时候，非常管用）
 * @param null $indexKey    作为返回数组的索引/键的列，它可以是该列的整数索引，或者字符串键值。
 * @return array            返回值
 */
function _array_column($input, $columnKey, $indexKey = null)
{
    if (!function_exists('array_column')) {
        $columnKeyIsNumber = (is_numeric($columnKey)) ? true : false;
        $indexKeyIsNull = (is_null($indexKey)) ? true : false;
        $indexKeyIsNumber = (is_numeric($indexKey)) ? true : false;
        $result = array();
        foreach ((array)$input as $key => $row) {
            if ($columnKeyIsNumber) {
                $tmp = array_slice($row, $columnKey, 1);
                $tmp = (is_array($tmp) && !empty($tmp)) ? current($tmp) : null;
            } else {
                $tmp = isset($row[$columnKey]) ? $row[$columnKey] : null;
            }
            if (!$indexKeyIsNull) {
                if ($indexKeyIsNumber) {
                    $key = array_slice($row, $indexKey, 1);
                    $key = (is_array($key) && !empty($key)) ? current($key) : null;
                    $key = is_null($key) ? 0 : $key;
                } else {
                    $key = isset($row[$indexKey]) ? $row[$indexKey] : 0;
                }
            }
            $result[$key] = $tmp;
        }
        return $result;
    } else {
        return array_column($input, $columnKey, $indexKey);
    }
}

/**
 * 将参数加密处理
 * 支持两种格式：
 * 1. 微信公众号格式：[公众号id, 用户id] => 公众号id_用户id
 * 2. 企业微信格式：[公众号id, 企业微信id, 用户id] => 公众号idq企业微信id_用户id
 */
function getEncryptParam($params)
{
    if (empty($params) || !is_array($params)) {
        return '';
    }

    if (count($params) == 2) {
        // 微信公众号格式：公众号id_用户id
        return $params[0] . '_' . $params[1];
    } elseif (count($params) >= 3) {
        // 企业微信格式：公众号idq企业微信id_用户id
        $result = $params[0] . 'q' . $params[1];
        if (isset($params[2])) {
            $result .= '_' . $params[2];
        }
        return $result;
    } else {
        // 只有一个参数，直接返回
        return $params[0];
    }
}

/**
 * 将接收的参数解密处理
 * 支持两种格式：
 * 1. 微信公众号格式：公众号id_用户id
 * 2. 企业微信格式：公众号idq企业微信id_用户id
 */
function getDecryptParam($param)
{
    $result = [];

    // 检查是否包含q符号（企业微信格式）
    if (strpos($param, 'q') !== false) {
        // 企业微信格式：公众号idq企业微信id_用户id
        // 先按q分割，获取公众号id和剩余部分
        $parts = explode('q', $param, 2);
        $result[0] = $parts[0]; // 公众号id

        if (isset($parts[1])) {
            // 剩余部分可能包含企业微信id和用户id（用_分割）
            $remaining = explode('_', $parts[1]);
            $result[1] = $remaining[0]; // 企业微信id

            // 如果还有用户id
            if (isset($remaining[1])) {
                $result[2] = $remaining[1]; // 用户id
            }
        }
    } else {
        // 原有格式，使用_分割：公众号id_用户id
        $result = explode('_', $param);
    }

    return $result;
}

/**
 * 查询公众号绑定的商户ID
 */
function getWxgzhBindMchId($configId)
{
    return Db::name('org_wxgzh')->where(['id' => $configId, 'status' => 1])->value('org_id');
}

function getUserType($openid)
{   
    $isWorkAuth = session('isWorkAuth');
    $map = [
        'openid' => $openid,
    ];
    if ($isWorkAuth) {
        $map['wx_type'] = 1;
    } else {
        $map['wx_type'] = 2;
    }
    
    $userRow = Db::name('org_wxuser')->where($map)->find();
    if (empty($userRow)) {
        return false;
    }
    return $userRow['type'];
}

/**
 * 地址路径
 * @param $group
 * @param $img_name
 * @param string $prefix
 * @return string
 */
function get_img_url($group, $img_name, $prefix = ''){
    $path = config('oa_img_url').'/static/upload/images/'.$group;
    if($prefix) {
        $url = $path.'/'.$prefix.'_'.$img_name;
    }else{
        $url = $path.'/'.$img_name;
    }
    return $url;
}

/**
 * 生成唯一的单据ID
 */
function getUUID($prefix = '')
{
    $time = time();
    return $prefix . date("ymd") . date("His") . rand(1000, 9999);
}

/**
 * 生产唯一的单据ID，不会有重复
 */
function getUUIDPro($prefix = '')
{
    // 获取缓存对象句柄
    $redis = Cache::store('redis')->handler();
    $key = $prefix . date("ymd") . date("His");
    $suffix = $redis->incr($key);
    $redis->expire($key, 60);
    $tmp = str_pad($suffix, 4, "0", STR_PAD_LEFT);
    return $key . $tmp;
}


/**
 * 处理微信out_trade_no
 */
function wx_out_trade_no($id){
    if(strpos($id, '-')){
        return explode('-', $id)[0];
    }else{
        return $id.'-'.time();
    }
}

    