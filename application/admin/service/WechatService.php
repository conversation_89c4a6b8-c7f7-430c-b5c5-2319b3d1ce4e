<?php

namespace app\admin\service;

use think\Db;
use EasyWeChat\Factory;

class WechatService
{
    /**
     * 获取公众号实例
     *
     * @return \EasyWeChat\OfficialAccount\Application
     * @throws \Exception
     */
    public static function getOfficialAccount()
    {
        $configId = session('wx_gzh_id');
        if (empty($configId)) {
            throw new \Exception('公众号配置不存在');
        }
        $config = Db::name('org_wxgzh')->where(['id' => $configId, 'status' => 1])->find();
        if (!$config) {
            throw new \Exception('公众号配置不存在');
        }

        return Factory::officialAccount([
            'app_id' => $config['account'],
            'secret' => $config['key'],
            'oauth' => [
                'scopes' => ['snsapi_userinfo'],
                'callback' => "auth/oauthCallback",
            ],
            'response_type' => 'array',
        ]);
    }

    /**
     * 获取企业微信实例
     *
     * @param int $configId 企业微信配置ID，如果为空则从session获取
     * @return \EasyWeChat\Work\Application
     * @throws \Exception
     */
    public static function getWork($configId = null)
    {
        if (empty($configId)) {
            $configId = session('wx_qy_id');
        }

        if (empty($configId)) {
            throw new \Exception('企业微信配置不存在');
        }

        $config = Db::name('org_qw')->where(['id' => $configId, 'status' => 1])->find();
        if (!$config) {
            throw new \Exception('企业微信配置不存在');
        }

        return Factory::work([
            'corp_id' => $config['corp_id'],
            'agent_id' => $config['agent_id'],
            'secret' => $config['secret'],
            'oauth' => [
                'scopes' => ['snsapi_userinfo'], // 非静默授权
                'callback' => 'auth/workCallback',
            ],
        ]);
    }

    /**
     * 验证公众号state参数是否有效
     *
     * @param string $state 公众号state参数
     * @return boolean
     */
    public static function validateState($state)
    {
        try {
            if (empty($state)) {
                return false;
            }
            $wx_gzh_id = getDecryptParam($state)[0];
            if (empty($wx_gzh_id)) {
                return false;
            }
            // 检查配置是否存在
            if (Db::name('org_wxgzh')->where(['id' => $wx_gzh_id, 'status' => 1])->count() > 0) {
                session('wx_gzh_id', $wx_gzh_id);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 验证经销商/群管state参数是否有效
     *
     * @param string $state 经销商/群管state参数
     * @param int $type 1=经销商，2=群管
     * @return boolean
     */
    public static function validateStateToUser($state, $type)
    {
        try {
            if (empty($state)) {
                return false;
            }

            // 确保公众号ID是正确的
            $wx_gzh_id = getDecryptParam($state)[0];
            if (empty($wx_gzh_id)) {
                return false;
            }
            // 检查配置是否存在
            $wx_gzh_row = Db::name('org_wxgzh')->where(['id' => $wx_gzh_id, 'status' => 1])->find();
            if (!$wx_gzh_row) {
                return false;
            }
            session('wx_gzh_id', $wx_gzh_id);

            $wx_user_id = getDecryptParam($state)[1];
            if (empty($wx_user_id)) {
                return false;
            }
            // 查询经销商/群管
            $userRow = Db::name('org_wxuser')->where(['user_id' => $wx_user_id, 'status' => 9, 'type' => $type])->find();
            if (!$userRow) {
                return false;
            }

            if ($type == 1) {
                // 群管注册，获取经销商ID
                $agency_id = Db::name('org_agency')->where(['wx_user_id' => $wx_user_id])->value('agency_id');
                session('wx_dealer_id', $agency_id);
            } elseif ($type == 2) {
                // 会员注册，获取群管ID和经销商ID
                $tubeRow = Db::name('org_agency_tube')->where(['wx_user_id' => $wx_user_id])->find();
                session('wx_dealer_id', $tubeRow['agency_id']);
                session('wx_group_id', $tubeRow['tube_id']);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 验证企业微信state参数是否有效
     *
     * @param string $state 企业微信state参数
     * @return boolean
     */
    public static function validateWorkState($state)
    {
        try {
            if (empty($state)) {
                return false;
            }

            $params = getDecryptParam($state);
            $wx_gzh_id = $params[0] ?? '';
            $wx_qy_id = $params[1] ?? '';

            if (empty($wx_gzh_id) || empty($wx_qy_id)) {
                return false;
            }

            // 检查公众号配置是否存在
            if (Db::name('org_wxgzh')->where(['id' => $wx_gzh_id, 'status' => 1])->count() == 0) {
                return false;
            }

            // 检查企业微信配置是否存在
            if (Db::name('org_qw')->where(['id' => $wx_qy_id, 'status' => 1])->count() == 0) {
                return false;
            }

            session('wx_gzh_id', $wx_gzh_id);
            session('wx_qy_id', $wx_qy_id);
            session('isWorkAuth', true);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 验证企业微信经销商/群管state参数是否有效
     *
     * @param string $state 企业微信经销商/群管state参数
     * @param int $type 1=经销商，2=群管
     * @return boolean
     */
    public static function validateWorkStateToUser($state, $type)
    {
        try {
            if (empty($state)) {
                return false;
            }

            $params = getDecryptParam($state);
            $wx_gzh_id = $params[0] ?? '';
            $wx_qy_id = $params[1] ?? '';
            $wx_user_id = $params[2] ?? '';

            if (empty($wx_gzh_id) || empty($wx_qy_id) || empty($wx_user_id)) {
                return false;
            }

            // 检查公众号配置是否存在
            $wx_gzh_row = Db::name('org_wxgzh')->where(['id' => $wx_gzh_id, 'status' => 1])->find();
            if (!$wx_gzh_row) {
                return false;
            }

            // 检查企业微信配置是否存在
            if (Db::name('org_qw')->where(['id' => $wx_qy_id, 'status' => 1])->count() == 0) {
                return false;
            }

            session('wx_gzh_id', $wx_gzh_id);
            session('wx_qy_id', $wx_qy_id);

            // 查询经销商/群管
            $userRow = Db::name('org_wxuser')->where(['user_id' => $wx_user_id, 'status' => 9, 'type' => $type])->find();
            if (!$userRow) {
                return false;
            }

            if ($type == 1) {
                // 群管注册，获取经销商ID
                $agency_id = Db::name('org_agency')->where(['wx_user_id' => $wx_user_id])->value('agency_id');
                session('wx_dealer_id', $agency_id);
            } elseif ($type == 2) {
                // 会员注册，获取群管ID和经销商ID
                $tubeRow = Db::name('org_agency_tube')->where(['wx_user_id' => $wx_user_id])->find();
                session('wx_dealer_id', $tubeRow['agency_id']);
                session('wx_group_id', $tubeRow['tube_id']);
            }
            session('isWorkAuth', true);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 判断state参数是否为企业微信格式
     *
     * @param string $state state参数
     * @return boolean
     */
    public static function isWorkState($state)
    {
        return !empty($state) && strpos($state, 'q') !== false;
    }

    /**
     * 获取企业微信非静默授权URL
     * 
     * 这个方法直接生成企业微信授权URL，强制使用非静默授权(snsapi_userinfo)
     * EasyWeChat 4.x在企业微信授权处理中的一个问题是，即使在配置中设置了snsapi_userinfo，
     * 实际生成的URL仍然可能使用snsapi_base(静默授权)，这个方法解决了这个问题
     *
     * @param string $redirectUrl 回调URL
     * @return string 授权URL
     * @throws \Exception
     */
    public static function getWorkOAuthUrl($redirectUrl = '')
    {
        $configId = session('wx_qy_id');
        if (empty($configId)) {
            throw new \Exception('企业微信配置不存在');
        }
        
        $config = Db::name('org_qw')->where(['id' => $configId, 'status' => 1])->find();
        if (!$config) {
            throw new \Exception('企业微信配置不存在');
        }
        
        // 如果没有提供回调URL，使用默认的
        if (empty($redirectUrl)) {
            $baseUrl = request()->domain();
            $redirectUrl = $baseUrl . '/auth/workCallback';
        }
        
        // 生成授权URL（强制使用非静默授权）
        $queries = [
            'appid' => $config['corp_id'],
            'redirect_uri' => $redirectUrl,
            'response_type' => 'code',
            'scope' => 'snsapi_userinfo', // 显式设置为非静默授权
            'agentid' => $config['agent_id'],
            'state' => md5(uniqid(mt_rand(), true)),
        ];
        
        return 'https://open.weixin.qq.com/connect/oauth2/authorize?' . http_build_query($queries) . '#wechat_redirect';
    }
}
