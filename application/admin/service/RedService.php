<?php

namespace app\admin\service;

use think\Db;
use EasyWeChat\Factory;

class RedService
{
    
    /**
     * 发红包
     */
    public function send($openid){

        $data = array();
        $time = time();

        //第1步：
        $config = config('resource.wxsh_duolian');//存的是duolian公众号
        $config['app_id'] = 'wx9aceb4784bfcdecb';//替换为润尔康惠民，因为:appid和openid要匹配
        $app = Factory::payment($config);

        $redpack = $payment->redpack;
        $redpackData = [
            'mch_billno'   => 'xy123456',
            'send_name'    => '测试红包',
            're_openid'    => 'oxTWIuGaIt6gTKsQRLau2M0yL16E',
            'total_num'    => 1,  //固定为1，可不传
            'total_amount' => 100,  //单位为分，不小于100
            'wishing'      => '祝福语',
            'client_ip'    => '***********',  //可不传，不传则由 SDK 取当前客户端 IP
            'act_name'     => '测试活动',
            'remark'       => '测试备注',
            // ...
        ];

        $result = $redpack->sendNormal($redpackData);





        $resp = $app->order->unify([
            'body' => '充值中心-会员充值',
            'out_trade_no' => wx_out_trade_no($data['order_id']),
            'total_fee' => $data['order_amount'] * 100,
            'notify_url' => $config['notify_url'], // 支付结果通知网址，如果不设置则会使用配置里的默认地址
            'trade_type' => 'JSAPI', // 交易类型：扫码支付不需要openid,JSAPI支付必须有openid
            'openid' => 'oJYdD6kUJiC74CtoQG00zNHjHDBk',
        ]);




        $params['agency_id'] = 1001009;

        $data['order_id'] = getUUIDPro('HB');
        $data['org_id'] = 101001;//$org_id;
        $data['order_status'] = 1;
        $data['agency_id'] = 1001009;//input('agency_id', 0);
        $data['obj'] = input('pay_obj', 0);
        $data['amount'] = 0.01;//input('amount', 0);
        $data['service_fee'] = round(0.01 * $data['amount'], 2);
        $data['order_amount'] = $data['amount'] + $data['service_fee'];
        // $data['operator_id'] = session('admin.id');
        // $data['operator_name'] = session('admin.name');
        $data['create_time'] = $time;
        $data['update_time'] = $time;
        //第一步：先创建订单
        if (!db('order')->insert($data)) {
            $this->error('创建订单失败');
        }

        // $order_id = 'CZ2505231541504471';
        $order_id = $data['order_id'];
        $order = db('order')->where('order_id', $data['order_id'])->find();
        //第二步：展示付款二维码
        $env = config('resource.wxsh_duolian');//存的是duolian公众号
        $env['app_id'] = 'wx9aceb4784bfcdecb';//替换为润尔康惠民，因为:appid和openid要匹配
        $app = Factory::payment($env);
        $resp = $app->order->unify([
            'body' => '充值中心-会员充值',
            'out_trade_no' => wx_out_trade_no($order['order_id']),
            'total_fee' => $order['order_amount'] * 100,
            'notify_url' => $env['notify_url'], // 支付结果通知网址，如果不设置则会使用配置里的默认地址
            'trade_type' => 'JSAPI', // 交易类型：扫码支付不需要openid,JSAPI支付必须有openid
            'openid' => 'oJYdD6oYUDsNTloOjRxwEmEdJ2cs',
        ]);

        //array(9) { ["return_code"]=> string(7) "SUCCESS" ["return_msg"]=> string(2) "OK" ["result_code"]=> string(7) "SUCCESS" ["mch_id"]=> string(10) "1716756646" ["appid"]=> string(18) "wx9aceb4784bfcdecb" ["nonce_str"]=> string(16) "GdQMl7TIz9Ixki8v" ["sign"]=> string(32) "121B234DEE17857D3AA94BA28B1F3FA1" ["prepay_id"]=> string(36) "wx22160654186827e0022361aec1951c0001" ["trade_type"]=> string(5) "JSAPI" }
        //
        if ($resp['return_code'] === 'SUCCESS') {// return_code 表示通信状态，不代表支付状态
            if ($resp['result_code'] === 'SUCCESS') {
                $prepayId = $resp['prepay_id'];
                $jssdk = $app->jssdk;
                // $json = $jssdk->bridgeConfig($prepayId); // 返回 json 字符串，如果想返回数组，传第二个参数 false
                // $wxConfig = $jssdk->sdkConfig($prepayId); // 返回数组
                // var_dump($wxConfig);
                // $this->assign("wxConfig", $wxConfig);
                // 
                $json = $jssdk->bridgeConfig($prepayId); // 返回 json 字符串，如果想返回数组，传第二个参数 false
                var_dump($json);
                $this->assign("json", $json);
                $this->assign("order", $order);
                return $this->fetch();
            // 用户支付失败
            } elseif ($message['result_code'] === 'FAIL') {
                echo 'error';
            }
        }
            var_dump($resp);
    }
}
