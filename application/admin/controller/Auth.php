<?php

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use app\admin\service\WechatService;

class Auth extends Controller
{
    protected $openid;
    protected function initialize()
    {
        parent::initialize();
        $isWorkAuth = session('isWorkAuth');
        if ($isWorkAuth) {
            $sessionKey = 'wechat_user_' . session('wx_qy_id');
        } else {
            $sessionKey = 'wechat_user_' . session('wx_gzh_id');
        }
        $this->openid = session("$sessionKey.openid");
    }

    /**
     * 经销商注册微信授权
     *
     * @return void
     */
    public function dealer()
    {
        // 接收公众号ID
        $state = Request::param('state');
        //验证state是否正确
        if (empty($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-101');
        }

        // 判断是否为企业微信授权并验证state参数
        if (WechatService::isWorkState($state)) {
            if (!WechatService::validateWorkState($state)) {
                return redirect('error/index')->with('error_msg', '无效的企业微信授权请求-102');
            }
        } else {
            if (!WechatService::validateState($state)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-102');
            }
        }
        if (!empty($this->openid)) {
            // 验证当前登录用户类型，是否是经销商，如果不是，直接阻止
            if (!getUserType($this->openid)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-103');
            }
            if (getUserType($this->openid) == 2) {
                return redirect('error/index')->with('error_msg', '你当前已注册群管，请勿重复注册');
            }
            if (getUserType($this->openid) == 3) {
                return redirect('error/index')->with('error_msg', '你当前已注册会员，请勿重复注册');
            }
            return redirect('register/dealer');
        }
        return $this->fetch();
    }

    /**
     * 群管注册微信授权
     *
     * @return void
     */
    public function groupMgr()
    {
        // 接收经销商ID
        $state = Request::param('state');
        //验证state是否正确
        if (empty($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-104');
        }

        // 判断是否为企业微信授权并验证state参数
        if (WechatService::isWorkState($state)) {
            if (!WechatService::validateWorkStateToUser($state, 1)) {
                return redirect('error/index')->with('error_msg', '无效的企业微信授权请求-105');
            }
        } else {
            if (!WechatService::validateStateToUser($state, 1)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-105');
            }
        }
        if (!empty($this->openid)) {
            // 验证当前登录用户类型，是否是群管，如果不是，直接阻止
            if (!getUserType($this->openid)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-106');
            }
            if (getUserType($this->openid) == 1) {
                return redirect('error/index')->with('error_msg', '你当前已注册经销商，请勿重复注册');
            }
            if (getUserType($this->openid) == 3) {
                return redirect('error/index')->with('error_msg', '你当前已注册会员，请勿重复注册');
            }
            return redirect('register/groupMgr');
        }
        return $this->fetch("group");
    }

    /**
     * 会员注册微信授权
     *
     * @return void
     */
    public function member()
    {
        // 接收群管ID
        $state = Request::param('state');
        //验证state是否正确
        if (empty($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-107');
        }

        // 判断是否为企业微信授权并验证state参数
        if (WechatService::isWorkState($state)) {
            if (!WechatService::validateWorkStateToUser($state, 2)) {
                return redirect('error/index')->with('error_msg', '无效的企业微信授权请求-108');
            }
        } else {
            if (!WechatService::validateStateToUser($state, 2)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-108');
            }
        }
        if (!empty($this->openid)) {
            // 验证当前登录用户类型，是否是会员，如果不是，直接阻止
            if (!getUserType($this->openid)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-109');
            }
            if (getUserType($this->openid) == 1) {
                return redirect('error/index')->with('error_msg', '你当前已注册经销商，请勿重复注册');
            }
            if (getUserType($this->openid) == 2) {
                return redirect('error/index')->with('error_msg', '你当前已注册群管，请勿重复注册');
            }
        }
        return redirect('register/member');
    }

    // 公众号授权回调
    public function oauthCallback()
    {
        try {
            $app = WechatService::getOfficialAccount();
            $user = $app->oauth->user();

            $userInfo = $user->toArray();
            $userInfo['openid'] = $user->getId();

            // 保存用户信息
            $sessionKey = 'wechat_user_' . session('wx_gzh_id');
            session($sessionKey, $userInfo);

            $target_url = session('target_url');
            return redirect($target_url);
        } catch (\Exception $e) {
            return redirect('error/index')->with('error_msg', '微信授权失败，请重试');
        }
    }

    // 企业微信授权回调
    public function workCallback()
    {
        try {
            // 获取授权code
            $code = Request::param('code');
            if (empty($code)) {
                return redirect('error/index')->with('error_msg', '企业微信授权失败：缺少授权码');
            }
            
            $workApp = WechatService::getWork();
            
            // 直接使用用户实例获取用户信息
            try {
                // 我们需要确保code存在于请求中，以便OAuth能正确处理
                // 因为无法直接修改底层请求对象，这里使用一个workaround方法
                $user = $workApp->oauth->user();
                
                if (empty($user->getId())) {
                    return redirect('error/index')->with('error_msg', '无法获取用户信息');
                }
            } catch (\Exception $e) {
                return redirect('error/index')->with('error_msg', '企业微信授权失败，请重试');
            }

            $userInfo = $user->toArray();
            
            // 确保ID字段存在
            if (isset($userInfo['id'])) {
                $userInfo['openid'] = $workApp->user->userIdToOpenid($userInfo['id'])['openid'];
            } elseif (isset($userInfo['userId'])) {
                $userInfo['openid'] = $workApp->user->userIdToOpenid($userInfo['userId'])['openid'];
            }

            // 保存用户信息到企业微信专用session
            $sessionKey = 'work_user_' . session('wx_qy_id');
            session($sessionKey, $userInfo);

            // 获取跳转目标URL
            $targetUrl = session('target_url');
            // 跳转回原页面
            return redirect($targetUrl);
        } catch (\Exception $e) {
            return redirect('error/index')->with('error_msg', '企业微信授权失败，请重试');
        }
    }
}
