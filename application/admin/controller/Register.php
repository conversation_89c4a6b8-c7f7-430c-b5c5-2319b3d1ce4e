<?php

namespace app\admin\controller;

use think\Controller;
use think\Db;
use app\admin\model\Dealer;
use app\admin\model\GroupMgr;
use app\admin\model\Member;

class Register extends Controller
{
    protected $openid;
    protected function initialize()
    {
        parent::initialize();
        $isWorkAuth = session('isWorkAuth');
        if ($isWorkAuth) {
            $sessionKey = 'wechat_user_' . session('wx_qy_id');
        } else {
            $sessionKey = 'wechat_user_' . session('wx_gzh_id');
        }
        $this->openid = session("$sessionKey.openid");
    }
    /**
     * 经销商注册申请页面展示
     *
     * @return void
     */
    public function dealer()
    {
        // 判断当前openid是否已经注册经销商
        $userRow = Db::name('org_wxuser')->where('openid', $this->openid)->find();
        if ($userRow) {
            $agencyRow = Db::name('org_agency')->where('wx_user_id', $userRow['user_id'])->find();
            if (in_array($agencyRow['status'], [1, 5])) {
                return redirect('register/dealerApply');
            }
            if ($agencyRow['status'] == 9) {
                return redirect('index/index');
            }
        }
        return $this->fetch();
    }

    /**
     * 群管注册申请页面展示
     *
     * @return void
     */
    public function groupMgr()
    {
        // 判断当前openid是否已经注册群管
        $userRow = Db::name('org_wxuser')->where('openid', $this->openid)->find();
        if ($userRow) {
            $agencyRow = Db::name('org_agency_tube')->where('wx_user_id', $userRow['user_id'])->find();
            if (in_array($agencyRow['status'], [1, 5])) {
                return redirect('register/groupApply');
            }
            if ($agencyRow['status'] == 9) {
                return redirect('index/index');
            }
        }
        return $this->fetch('group');
    }

    /**
     * 会员注册申请页面展示
     *
     * @return void
     */
    public function member()
    {
        // 注册会员逻辑，注册完成后，跳转到会员页面
        $Member = new Member();
        $userRow = $Member->getOrCreateWxUser($this->openid);
        if ($userRow['code'] != 200) {
            return redirect('error/index')->with('error_msg', $userRow['msg']);
        }
        $userRow = $userRow['data'];
        // 注册成功，直接跳转会员页面
        return redirect('user/info');
    }

    /**
     * 经销商注册申请提交
     *
     * @return void
     */
    public function dealerApply()
    {
        $Dealer = new Dealer();
        Db::startTrans();
        try {
            $result = $Dealer->getOrCreateWxUser($this->openid);
            if ($result['code'] != 200) {
                return redirect('error/index')->with('error_msg', $result['msg']);
            }
            $userRow = $result['data'];
            if (!$userRow) {
                return redirect('error/index')->with('error_msg', '获取微信用户信息失败');
            }
            $agencyRow = $Dealer->getOrCreateAgency($userRow['user_id']);
            if (!$agencyRow) {
                return redirect('error/index')->with('error_msg', '创建经销商信息失败');
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return redirect('error/index')->with('error_msg', $e->getMessage());
        }
        // 审核通过，直接跳转首页
        if ($agencyRow['status'] == 9) {
            return redirect('index/index');
        }
        $this->assign("userRow", $userRow);
        return $this->fetch();
    }

    /**
     * 经销商审核重新提交
     */
    public function dealerReapply()
    {
        // 获取微信用户信息
        $userRow = Db::name('org_wxuser')->where('openid', $this->openid)->find();
        if (!$userRow) {
            return redirect('error/index')->with('error_msg', '获取用户信息失败');
        }

        // 获取关联的经销商信息
        $agencyRow = Db::name('org_agency')->where('wx_user_id', $userRow['user_id'])->find();
        if (!$agencyRow) {
            return redirect('error/index')->with('error_msg', '未找到经销商信息');
        }

        // 验证状态是否允许重新提交
        if ($userRow['status'] != 5 || $agencyRow['status'] != 5) {
            return redirect('error/index')->with('error_msg', '当前状态不允许重新提交');
        }

        // 重新提交申请
        Db::startTrans();
        try {
            $currentTime = time();
            
            // 更新用户状态
            Db::name('org_wxuser')->update([
                'user_id' => $userRow['user_id'],
                'status' => 1,
                'update_time' => $currentTime
            ]);

            // 更新经销商状态
            Db::name('org_agency')->update([
                'agency_id' => $agencyRow['agency_id'],
                'status' => 1,
                'update_time' => $currentTime
            ]);

            Db::commit();
            return redirect('register/dealerApply');
            
        } catch (\Exception $e) {
            Db::rollback();
            return redirect('error/index')->with('error_msg', $e->getMessage());
        }
    }

    /**
     * 群管注册申请提交
     *
     * @return void
     */
    public function groupApply()
    {
        $GroupMgr = new GroupMgr();
        Db::startTrans();
        try {
            $result = $GroupMgr->getOrCreateWxUser($this->openid);
            if ($result['code'] != 200) {
                return redirect('error/index')->with('error_msg', $result['msg']);
            }
            $userRow = $result['data'];
            $agencyRow = $GroupMgr->getOrCreateGroupMgr($userRow['user_id']);
            if (!$agencyRow) {
                return redirect('error/index')->with('error_msg', '创建群管信息失败');
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return redirect('error/index')->with('error_msg', $e->getMessage());
        }
        // 审核通过，直接跳转首页
        if ($agencyRow['status'] == 9) {
            return redirect('index/index');
        }
        $this->assign("userRow", $userRow);
        return $this->fetch();
    }

    /**
     * 群管审核重新提交
     */
    public function groupReapply()
    {
        // 获取微信用户信息
        $userRow = Db::name('org_wxuser')->where('openid', $this->openid)->find();
        if (!$userRow) {
            return redirect('error/index')->with('error_msg', '获取用户信息失败');
        }

        // 获取关联的群管信息
        $tubeRow = Db::name('org_agency_tube')->where('wx_user_id', $userRow['user_id'])->find();
        if (!$tubeRow) {
            return redirect('error/index')->with('error_msg', '未找到群管信息');
        }

        // 验证状态是否允许重新提交
        if ($userRow['status'] != 5 || $tubeRow['status'] != 5) {
            return redirect('error/index')->with('error_msg', '当前状态不允许重新提交');
        }

        // 重新提交申请
        Db::startTrans();
        try {
            $currentTime = time();
            
            // 更新用户状态
            Db::name('org_wxuser')->update([
                'user_id' => $userRow['user_id'],
                'status' => 1,
                'update_time' => $currentTime
            ]);

            // 更新经销商状态
            Db::name('org_agency_tube')->update([
                'tube_id' => $tubeRow['tube_id'],
                'status' => 1,
                'update_time' => $currentTime
            ]);

            Db::commit();
            return redirect('register/groupApply');
            
        } catch (\Exception $e) {
            Db::rollback();
            return redirect('error/index')->with('error_msg', $e->getMessage());
        }
    }
}
