<?php
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use EasyWeChat\Factory;

class Order extends Controller
{

    // protected function initialize()
    // {
    //     parent::initialize();

    //     // 设置当前标签
    //     $this->assign('navtab', 'course');
    // }

    /**
     * 获取二维码信息
     */
    public function qrcode()
    {
        $params = input();
        $content = $params['content'];
        $size = $params['size'];
        require_once "./../vendor/qrcode/phpqrcode.php";
        //纠错级别， 纠错级别越高，生成图片会越大
        //L水平    7%的字码可被修正
        //M水平    15%的字码可被修正
        //Q水平    25%的字码可被修正
        //H水平    30%的字码可被修正
        $level = "L";
        //图片每个黑点的像素。
        //生成图片 第二个参数：是否保存成文件 如需要保存文件，第二个参数改为文件名即可,如：'qrcode.png'
        \QRcode::png($content, false, $level, $size);
    }

    /**
     * 充值
     */
    public function recharge(){
        $params = input();
        $data = array();
        $time = time();
        $action = "BalanceRecharge";
        $params['agency_id'] = 1001009;
        //如果是传入agency_id，给经销商充值，如果是没有传入给商户充值
        // if (isset($params['agency_id']) && !empty($params['agency_id'])) {
        //     $row = db('org_agency t')
        //         ->leftJoin("org_wxgzh w","t.wxgzh_id = w.id")
        //         ->field("t.*, w.name gzh_name")
        //         ->where('agency_id', $params['agency_id'])->find();
        //     // if (empty($row) or $row['org_id'] != $this->org_id) {
        //     //     $this->error('信息有误，请重新操作！', url('index'));
        //     // }
        //     var_dump($row);exit;
        //     $model = [
        //         'name' => $row['agency_name'],
        //         'agency_id' => $row['agency_id'],
        //     ];
        //     $this->assign('model', $model);
        // }else{
        //     $row = db('org t')->where('org_id', $org_id)->find();
        //     $model = [
        //         'name' => $row['name'],
        //     ];
        //     $this->assign('model', $model);
        // }

        // if (Request::isPost()) {
            // 检查页面提交的与Session储存的Token是否一致，防止重复提交
            // if(!empty($_REQUEST['form_token'])){
            //     Helper::checkFormToken($action);
            // }

            $data['order_id'] = getUUID('CZ');
            $data['org_id'] = 101001;//$org_id;
            $data['order_status'] = 1;
            $data['agency_id'] = 1001009;//input('agency_id', 0);
            $data['obj'] = input('pay_obj', 0);
            $data['amount'] = 0.01;//input('amount', 0);
            $data['service_fee'] = round(0.01 * $data['amount'], 2);
            $data['order_amount'] = $data['amount'] + $data['service_fee'];
            // $data['operator_id'] = session('admin.id');
            // $data['operator_name'] = session('admin.name');
            $data['create_time'] = $time;
            $data['update_time'] = $time;
            //第一步：先创建订单
            if (!db('order')->insert($data)) {
                $this->error('创建订单失败');
            }

            // $order_id = 'CZ2505231541504471';
            $order_id = $data['order_id'];
            $order = db('order')->where('order_id', $data['order_id'])->find();
            //第二步：展示付款二维码
            $env = config('resource.wxsh_duolian');//存的是duolian公众号
            $env['app_id'] = 'wx9aceb4784bfcdecb';//替换为润尔康惠民，因为:appid和openid要匹配
            $app = Factory::payment($env);
            $resp = $app->order->unify([
                'body' => '充值中心-会员充值',
                'out_trade_no' => wx_out_trade_no($order['order_id']),
                'total_fee' => $order['order_amount'] * 100,
                'notify_url' => $env['notify_url'], // 支付结果通知网址，如果不设置则会使用配置里的默认地址
                'trade_type' => 'JSAPI', // 交易类型：扫码支付不需要openid,JSAPI支付必须有openid
                'openid' => 'oJYdD6oYUDsNTloOjRxwEmEdJ2cs',
            ]);

            //array(9) { ["return_code"]=> string(7) "SUCCESS" ["return_msg"]=> string(2) "OK" ["result_code"]=> string(7) "SUCCESS" ["mch_id"]=> string(10) "1716756646" ["appid"]=> string(18) "wx9aceb4784bfcdecb" ["nonce_str"]=> string(16) "GdQMl7TIz9Ixki8v" ["sign"]=> string(32) "121B234DEE17857D3AA94BA28B1F3FA1" ["prepay_id"]=> string(36) "wx22160654186827e0022361aec1951c0001" ["trade_type"]=> string(5) "JSAPI" }
            //
            if ($resp['return_code'] === 'SUCCESS') {// return_code 表示通信状态，不代表支付状态
                if ($resp['result_code'] === 'SUCCESS') {
                    $prepayId = $resp['prepay_id'];
                    $jssdk = $app->jssdk;
                    // $json = $jssdk->bridgeConfig($prepayId); // 返回 json 字符串，如果想返回数组，传第二个参数 false
                    // $wxConfig = $jssdk->sdkConfig($prepayId); // 返回数组
                    // var_dump($wxConfig);
                    // $this->assign("wxConfig", $wxConfig);
                    // 
                    $json = $jssdk->bridgeConfig($prepayId); // 返回 json 字符串，如果想返回数组，传第二个参数 false
                    var_dump($json);
                    $this->assign("json", $json);
                    $this->assign("order", $order);
                    return $this->fetch();
                // 用户支付失败
                } elseif ($message['result_code'] === 'FAIL') {
                    echo 'error';
                }
            }

            var_dump($resp);

            // $jssdk = $app->jssdk;

        //     //第二步：跳转到支付页面
        //     $this->redirect('Order/doPay', ['order_id'=>$data['order_id']]);
        // } else {
        //     $this->assign("form_token", Helper::initFormToken($action));
        //     return $this->fetch();
        // }
    }

    /**
     * 充值
     */
    public function wxpay(){
        $params = input();
        $data = array();
        $time = time();
        $action = "BalanceRecharge";
        $params['agency_id'] = 1001009;

        $data['order_id'] = getUUID('CZ');
        $data['org_id'] = 101001;//$org_id;
        $data['order_status'] = 1;
        $data['agency_id'] = 1001009;//input('agency_id', 0);
        $data['obj'] = input('pay_obj', 0);
        $data['amount'] = 0.01;//input('amount', 0);
        $data['service_fee'] = round(0.01 * $data['amount'], 2);
        $data['order_amount'] = $data['amount'] + $data['service_fee'];
        // $data['operator_id'] = session('admin.id');
        // $data['operator_name'] = session('admin.name');
        $data['create_time'] = $time;
        $data['update_time'] = $time;
        //第一步：先创建订单
        if (!db('order')->insert($data)) {
            $this->error('创建订单失败');
        }

        //第二步：展示付款二维码
        $config = config('resource.wxsh_duolian');//存的是duolian公众号
        $config['app_id'] = 'wx9aceb4784bfcdecb';//替换为润尔康惠民，因为:appid和openid要匹配
        $app = Factory::payment($config);
        $resp = $app->order->unify([
            'body' => '充值中心-会员充值',
            'out_trade_no' => wx_out_trade_no($data['order_id']),
            'total_fee' => $data['order_amount'] * 100,
            'notify_url' => $config['notify_url'], // 支付结果通知网址，如果不设置则会使用配置里的默认地址
            'trade_type' => 'JSAPI', // 交易类型：扫码支付不需要openid,JSAPI支付必须有openid
            'openid' => 'oJYdD6kUJiC74CtoQG00zNHjHDBk',
        ]);

        //array(9) { ["return_code"]=> string(7) "SUCCESS" ["return_msg"]=> string(2) "OK" ["result_code"]=> string(7) "SUCCESS" ["mch_id"]=> string(10) "1716756646" ["appid"]=> string(18) "wx9aceb4784bfcdecb" ["nonce_str"]=> string(16) "GdQMl7TIz9Ixki8v" ["sign"]=> string(32) "121B234DEE17857D3AA94BA28B1F3FA1" ["prepay_id"]=> string(36) "wx22160654186827e0022361aec1951c0001" ["trade_type"]=> string(5) "JSAPI" }
        //
        if ($resp['return_code'] === 'SUCCESS') {// return_code 表示通信状态，不代表支付状态
            if ($resp['result_code'] === 'SUCCESS') {
                $prepayId = $resp['prepay_id'];
                $jssdk = $app->jssdk;
                $wxInit = $jssdk->sdkConfig($prepayId);
                $wxConfig = $jssdk->buildConfig(array('chooseWXPay'), true); // 返回数组
                var_dump($wxInit, $wxConfig);
                $this->assign("wxConfig", $wxConfig);
                
                return $this->fetch();
            // 用户支付失败
            } elseif ($message['result_code'] === 'FAIL') {
                echo 'error';
            }
        }

            var_dump($resp);

    }

    
    /**
     * 去支付
     */
    public function doPay(){
        $params = input();
        $time = time();
        $order_id = $params['order_id'];
        $order = db('order')->where('order_id', $order_id)->find();
        if (empty($order) ) {
            $this->error('信息有误，请重新操作！', url('Index/index'));
        }
        if($order['order_status'] == 5){
            $this->success('订单支付成功!!!', url('Balance/index'));
        }
        //第二步：展示付款二维码
        $config = config('resource.wxsh_duolian');
        $app = Factory::payment($config);
        $resp = $app->order->unify([
            'body' => '充值中心-会员充值',
            'out_trade_no' => wx_out_trade_no($order['order_id']),
            'total_fee' => $order['order_amount'] * 100,
            'notify_url' => $config['notify_url'], // 支付结果通知网址，如果不设置则会使用配置里的默认地址
            'trade_type' => 'JSAPI', // 交易类型：扫码支付不需要openid,JSAPI支付必须有openid
            'openid' => 'oUpF8uMuAJO_M2pxb1Q9zNjWeS6o',
        ]);
        // var_dump(wx_out_trade_no($order['order_id']), $config, $resp);
        $this->assign('resp', $resp);
        $this->assign('order', $order);
        return $this->fetch('balance/pay');
    }

    /**
     * 关闭订单
     */
    public function close(){
        $params = input();
        $time = time();
        $order_id = $params['order_id'];
        $order = db('order')->where('order_id', $order_id)->find();
        if (empty($order) or $order['order_status'] != 1) {
            $this->error('信息有误，请重新操作！', url('Balance/index'));
        }
        $data = [
            'order_id' => $order_id,
            'order_status' => 4,
            'update_time' => $time,
        ];
        if (db('order')->update($data) === false) {
            $this->error('更新失败');
        }
        $this->success('关闭成功!!!', url('Order/index'));
    }
}